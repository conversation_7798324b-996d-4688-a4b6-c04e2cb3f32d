from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium import webdriver
import pandas as pd
import time
import logging
import os
import datetime
import re
from selenium.common.exceptions import WebDriverException, TimeoutException, NoSuchElementException

# Configuração de log
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('g_finder.log')
    ]
)

logger = logging.getLogger('G-Finder')

def verificar_webdriver_ativo(driver):
    """
    Verifica se o WebDriver ainda está ativo e funcionando

    Args:
        driver: WebDriver do Selenium

    Returns:
        bool: True se o driver estiver ativo, False caso contrário
    """
    try:
        # Tenta obter o título da página atual
        driver.current_url
        return True
    except WebDriverException:
        logger.error("WebDriver não está mais ativo")
        return False
    except Exception as e:
        logger.error(f"Erro ao verificar status do WebDriver: {str(e)}")
        return False

def buscar_cep(driver, cep):
    """
    Busca um CEP no Google Maps

    Args:
        driver: WebDriver do Selenium
        cep: String contendo o CEP a ser buscado
    """
    try:
        logger.info(f"Buscando pelo CEP: {cep}")

        # Limpa o texto prévio e espera pelo campo de busca
        search_box = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.XPATH, '//*[@id="searchboxinput"]'))
        )
        search_box.clear()

        # Formata o CEP e insere no campo de busca
        cep_formatado = re.sub(r'[^0-9]', '', cep)  # Remove caracteres não numéricos
        if len(cep_formatado) == 8:
            cep_formatado = f"{cep_formatado[:5]}-{cep_formatado[5:]}"

        search_box.send_keys(cep_formatado)
        search_box.send_keys(Keys.ENTER)

        # Espera para carregar os resultados
        logger.info("Aguardando resultados do CEP...")
        time.sleep(5)

        # Verifica se o CEP foi encontrado
        try:
            WebDriverWait(driver, 5).until(
                EC.presence_of_element_located((By.XPATH, '//h1[contains(@class, "fontHeadlineLarge")]'))
            )
            logger.info(f"CEP {cep} encontrado com sucesso")
            return True
        except:
            logger.warning(f"Não foi possível confirmar se o CEP {cep} foi encontrado corretamente")
            return True  # Assume que o CEP foi encontrado mesmo sem confirmação

    except Exception as e:
        logger.error(f'Falha ao buscar CEP {cep}: {str(e)}')
        return False

def buscar_palavra_chave(driver, palavra_chave):
    """
    Busca uma palavra-chave no Google Maps na região atual

    Args:
        driver: WebDriver do Selenium
        palavra_chave: String contendo o termo a ser buscado
    """
    try:
        logger.info(f"Buscando pela palavra-chave: {palavra_chave}")

        # Espera pela presença do campo de busca
        search_box = WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.XPATH, '//*[@id="searchboxinput"]'))
        )

        # Limpa o texto anterior
        search_box.send_keys(Keys.CONTROL + "a")
        search_box.send_keys(Keys.DELETE)

        # Insere a palavra-chave
        search_box.send_keys(palavra_chave)
        search_box.send_keys(Keys.ENTER)

        # Aguarda pelo carregamento dos resultados
        logger.info("Aguardando resultados da busca...")
        time.sleep(5)

        # Verifica se há resultados com múltiplos seletores
        try:
            # Tentar múltiplos seletores para encontrar resultados
            selectors = [
                '//a[@class="hfpxzc"]',
                '//a[contains(@class, "hfpxzc")]',
                '//div[@role="article"]//a',
                '//div[contains(@class, "Nv2PK")]//a',
                '//div[@data-result-index]//a'
            ]

            resultados = None
            for selector in selectors:
                try:
                    resultados = WebDriverWait(driver, 5).until(
                        EC.presence_of_all_elements_located((By.XPATH, selector))
                    )
                    if resultados and len(resultados) > 0:
                        logger.info(f"Encontrados {len(resultados)} resultados iniciais para '{palavra_chave}' usando seletor: {selector}")
                        return True
                except:
                    continue

            logger.warning(f"Não foram encontrados resultados para '{palavra_chave}' com nenhum seletor")
            return False
        except Exception as e:
            logger.error(f"Erro ao verificar resultados: {str(e)}")
            return False

    except Exception as e:
        logger.error(f'Falha ao buscar palavra-chave {palavra_chave}: {str(e)}')
        return False

def extrair_clientes(driver, quantidade_desejada, progress_callback):
    """
    Extrai informações de clientes com base nos resultados da busca

    Args:
        driver: WebDriver do Selenium
        quantidade_desejada: Número de clientes a serem extraídos
        progress_callback: Função de callback para atualizar o progresso

    Returns:
        Lista de dicionários contendo informações dos clientes
    """
    clientes_extraidos = []
    clientes_unicos = set()
    contador = 0
    tentativas_sem_novos = 0
    MAX_TENTATIVAS_SEM_NOVOS = 10

    # Obter a palavra-chave atual da busca para uso em caso de recuperação
    try:
        palavra_chave_atual = driver.find_element(By.XPATH, '//*[@id="searchboxinput"]').get_attribute('value')
    except:
        palavra_chave_atual = "Loja"  # Valor padrão caso não consiga obter

    logger.info(f"Iniciando extração de {quantidade_desejada} leads...")

    try:
        # Contador para tentativas consecutivas de encontrar elementos
        tentativas_encontrar_elementos = 0
        MAX_TENTATIVAS_ENCONTRAR_ELEMENTOS = 5

        # Loop principal para extrair clientes
        while contador < quantidade_desejada and tentativas_sem_novos < MAX_TENTATIVAS_SEM_NOVOS:
            # Verificar se o WebDriver ainda está ativo
            if not verificar_webdriver_ativo(driver):
                logger.error("WebDriver não está mais ativo. Interrompendo extração.")
                break
            # Coleta todos os elementos de resultados visíveis na página com múltiplos seletores
            elementos = None
            selectors = [
                '//a[@class="hfpxzc"]',
                '//a[contains(@class, "hfpxzc")]',
                '//div[@role="article"]//a',
                '//div[contains(@class, "Nv2PK")]//a',
                '//div[@data-result-index]//a',
                '//div[contains(@class, "THOPZb")]//a'
            ]

            try:
                for selector in selectors:
                    try:
                        elementos = WebDriverWait(driver, 3).until(
                            EC.presence_of_all_elements_located((By.XPATH, selector))
                        )
                        if elementos and len(elementos) > 0:
                            logger.info(f"Encontrados {len(elementos)} elementos na página atual usando seletor: {selector}")
                            tentativas_encontrar_elementos = 0  # Resetar contador se encontrou elementos
                            break
                    except:
                        continue

                if not elementos or len(elementos) == 0:
                    raise Exception("Nenhum elemento encontrado com os seletores disponíveis")

            except:
                logger.warning("Não foi possível encontrar elementos na página atual")
                tentativas_encontrar_elementos += 1

                # Se falhar muitas vezes consecutivas, tenta recarregar a página
                if tentativas_encontrar_elementos >= MAX_TENTATIVAS_ENCONTRAR_ELEMENTOS:
                    logger.warning(f"Falhou {MAX_TENTATIVAS_ENCONTRAR_ELEMENTOS} vezes consecutivas ao tentar encontrar elementos. Tentando recarregar a página.")
                    try:
                        # Recarregar a página e refazer a busca
                        driver.get('https://www.google.com/maps/')
                        time.sleep(3)
                        search_box = WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.XPATH, '//*[@id="searchboxinput"]'))
                        )
                        search_box.send_keys(palavra_chave_atual)
                        search_box.send_keys(Keys.ENTER)
                        time.sleep(5)
                        logger.info("Página recarregada após falhas consecutivas")
                        tentativas_encontrar_elementos = 0  # Resetar contador
                    except Exception as reload_error:
                        logger.error(f"Erro ao recarregar a página: {str(reload_error)}")
                        # Se não conseguir recarregar, interrompe a extração
                        break

                scroll_down(driver)
                time.sleep(2)
                continue

            # Se já processamos todos os elementos visíveis, role para baixo para carregar mais
            if contador >= len(elementos):
                logger.info("Rolando para baixo para carregar mais resultados...")

                # Guardar o número atual de elementos antes do scroll
                elementos_antes_scroll = len(elementos)

                scroll_down(driver)
                time.sleep(3)  # Aguardar mais tempo para carregamento

                # Verificar se novos elementos foram carregados após o scroll
                try:
                    elementos_apos_scroll = None
                    for selector in selectors:
                        try:
                            elementos_apos_scroll = driver.find_elements(By.XPATH, selector)
                            if elementos_apos_scroll and len(elementos_apos_scroll) > 0:
                                break
                        except:
                            continue

                    if elementos_apos_scroll and len(elementos_apos_scroll) > elementos_antes_scroll:
                        logger.info(f"Novos elementos carregados: {len(elementos_apos_scroll)} (antes: {elementos_antes_scroll})")
                        tentativas_sem_novos = 0  # Resetar contador se encontrou novos elementos
                    else:
                        tentativas_sem_novos += 1
                        logger.warning(f"Nenhum novo elemento carregado após scroll. Tentativa {tentativas_sem_novos}/{MAX_TENTATIVAS_SEM_NOVOS}")

                        # Se não há novos elementos por várias tentativas, pode ter chegado ao fim
                        if tentativas_sem_novos >= MAX_TENTATIVAS_SEM_NOVOS:
                            logger.info("Máximo de tentativas sem novos elementos atingido. Finalizando extração.")
                            break

                except Exception as e:
                    logger.error(f"Erro ao verificar novos elementos após scroll: {str(e)}")
                    tentativas_sem_novos += 1

                continue

            # Tenta clicar no elemento para abrir os detalhes
            try:
                logger.info(f"Clicando no resultado {contador + 1}")

                # Verificar se o WebDriver ainda está ativo antes de clicar
                if not verificar_webdriver_ativo(driver):
                    logger.error("WebDriver não está mais ativo durante clique. Interrompendo.")
                    break

                # Rolar o elemento para o centro da tela
                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", elementos[contador])
                time.sleep(1)

                # Tentar remover elementos sobrepostos que podem interferir no clique
                try:
                    # Remover botões que podem estar sobrepostos
                    driver.execute_script("""
                        var overlayButtons = document.querySelectorAll('button.e2moi');
                        overlayButtons.forEach(function(button) {
                            if (button.style) {
                                button.style.display = 'none';
                            }
                        });
                    """)
                    time.sleep(0.5)
                except:
                    pass

                # Tentar múltiplas estratégias de clique
                clique_sucesso = False

                # Estratégia 1: Clique normal
                try:
                    elementos[contador].click()
                    clique_sucesso = True
                except Exception as e1:
                    logger.warning(f"Clique normal falhou: {str(e1)}")

                    # Estratégia 2: Clique via JavaScript
                    try:
                        driver.execute_script("arguments[0].click();", elementos[contador])
                        clique_sucesso = True
                        logger.info("Clique via JavaScript bem-sucedido")
                    except Exception as e2:
                        logger.warning(f"Clique via JavaScript falhou: {str(e2)}")

                        # Estratégia 3: Clique com ActionChains
                        try:
                            from selenium.webdriver.common.action_chains import ActionChains
                            ActionChains(driver).move_to_element(elementos[contador]).click().perform()
                            clique_sucesso = True
                            logger.info("Clique via ActionChains bem-sucedido")
                        except Exception as e3:
                            logger.warning(f"Clique via ActionChains falhou: {str(e3)}")

                if clique_sucesso:
                    time.sleep(3)  # Aguardar carregamento da página de detalhes
                    tentativas_sem_novos = 0  # Resetar contador de tentativas se conseguiu clicar
                else:
                    logger.error(f"Todas as estratégias de clique falharam para o resultado {contador + 1}")
                    contador += 1
                    continue
            except WebDriverException as e:
                logger.error(f"Erro do WebDriver ao clicar no resultado {contador + 1}: {str(e)}")
                # Se for erro do WebDriver, pode ser que a sessão foi perdida
                if not verificar_webdriver_ativo(driver):
                    logger.error("Sessão do WebDriver foi perdida. Interrompendo extração.")
                    break
                contador += 1
                continue
            except Exception as e:
                logger.warning(f"Não foi possível clicar no resultado {contador + 1}: {str(e)}")
                contador += 1
                continue

            # Extrai as informações do cliente
            try:
                # Extrair o nome com múltiplos seletores
                nome_selectors = [
                    '//*[@id="QA0Szd"]/div/div/div[1]/div[3]/div/div[1]/div/div/div[2]/div[2]/div/div[1]/div[1]/h1',
                    '//h1[contains(@class, "DUwDvf")]',
                    '//h1[@data-attrid="title"]',
                    '//div[contains(@class, "lMbq3e")]//h1',
                    '//div[@role="main"]//h1',
                    '//h1[contains(@class, "x3AX1")]',
                    '//div[contains(@class, "TIHn2")]//h1'
                ]

                nome_cliente = None
                for selector in nome_selectors:
                    try:
                        nome_elemento = WebDriverWait(driver, 3).until(
                            EC.presence_of_element_located((By.XPATH, selector))
                        )
                        nome_cliente = nome_elemento.text.strip()
                        if nome_cliente:
                            break
                    except:
                        continue

                if not nome_cliente:
                    raise Exception("Não foi possível extrair o nome do estabelecimento")

                # Pular se o cliente já foi processado
                if nome_cliente in clientes_unicos:
                    logger.info(f"Cliente '{nome_cliente}' já foi processado, pulando...")
                    contador += 1
                    continue

                # Extrair informações adicionais
                cliente = {
                    'nome': nome_cliente,
                    'telefone': "Telefone não disponível",
                    'endereco': "Endereço não disponível",
                    'site': "Site não disponível"
                }

                # Função auxiliar para limpar e formatar números de telefone
                def limpar_telefone(texto):
                    # Remover textos específicos que não são telefones
                    if not texto or texto.strip() == "":
                        return "Telefone não disponível"

                    texto = re.sub(r'Enviar para o smartphone', '', texto, flags=re.IGNORECASE)

                    # Extrair apenas dígitos, parênteses, traços e espaços
                    texto_limpo = re.sub(r'[^\d\(\)\s\-\+]', '', texto)

                    # Aplicar regex para extrair apenas o número de telefone
                    telefone_match = re.search(r'(?:\+?55\s?)?(?:\(?\d{2}\)?[\s.-]?)?\d{4,5}[\s.-]?\d{4}', texto_limpo)

                    if telefone_match:
                        # Apenas extrair o número encontrado
                        return telefone_match.group(0)

                    # Se não encontrar um padrão válido de telefone, verificar se pelo menos tem dígitos
                    digitos = re.sub(r'\D', '', texto_limpo)
                    if len(digitos) >= 8:  # Telefone tem pelo menos 8 dígitos
                        # Formatar o número encontrado
                        if len(digitos) >= 10:  # Com DDD
                            return f"({digitos[:2]}) {digitos[2:6]}-{digitos[6:]}"
                        else:  # Sem DDD
                            return f"{digitos[:4]}-{digitos[4:]}"

                    return "Telefone não disponível"

                # Extrair telefone
                try:
                    # Primeira tentativa - capturar do botão com data-item-id específico para telefone
                    telefone_elemento = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, '//button[@data-item-id[contains(., "phone:tel:")]]'))
                    )
                    # Extrair do aria-label que contém o número formatado
                    aria_label = telefone_elemento.get_attribute('aria-label')
                    if aria_label and "telefone:" in aria_label.lower():
                        # Extrair o número do aria-label
                        telefone_texto = aria_label.split(':', 1)[1].strip()
                        cliente['telefone'] = limpar_telefone(telefone_texto)
                    else:
                        # Capturar do texto do elemento interno
                        try:
                            telefone_interno = telefone_elemento.find_element(By.XPATH, './/div[contains(@class, "Io6YTe") or contains(@class, "fontBodyMedium")]')
                            telefone_texto = telefone_interno.text.strip()
                            cliente['telefone'] = limpar_telefone(telefone_texto)
                        except:
                            # Se não encontrar o elemento interno, usar o texto do botão
                            telefone_texto = telefone_elemento.text.strip()
                            cliente['telefone'] = limpar_telefone(telefone_texto)
                except:
                    try:
                        # Segunda tentativa - capturar do botão com classe CsEnBe que tem aria-label contendo "Telefone:"
                        telefone_elemento = WebDriverWait(driver, 3).until(
                            EC.presence_of_element_located((By.XPATH, '//button[contains(@class, "CsEnBe") and contains(@aria-label, "Telefone:")]'))
                        )
                        aria_label = telefone_elemento.get_attribute('aria-label')
                        if aria_label:
                            # Extrair o número do aria-label
                            telefone_texto = aria_label.split(':', 1)[1].strip()
                            cliente['telefone'] = limpar_telefone(telefone_texto)
                        else:
                            # Tentar capturar da div interna
                            try:
                                telefone_interno = telefone_elemento.find_element(By.XPATH, './/div[contains(@class, "rogA2c")]//div[contains(@class, "fontBodyMedium")]')
                                telefone_texto = telefone_interno.text.strip()
                                cliente['telefone'] = limpar_telefone(telefone_texto)
                            except:
                                telefone_texto = telefone_elemento.text.strip()
                                cliente['telefone'] = limpar_telefone(telefone_texto)
                    except:
                        try:
                            # Terceira tentativa - capturar de links href="tel:"
                            telefone_elemento = WebDriverWait(driver, 3).until(
                                EC.presence_of_element_located((By.XPATH, '//a[contains(@href, "tel:")]'))
                            )
                            href = telefone_elemento.get_attribute('href')
                            if href and 'tel:' in href:
                                telefone_texto = href.split('tel:')[1].strip()
                                cliente['telefone'] = limpar_telefone(telefone_texto)
                            else:
                                telefone_texto = telefone_elemento.text.strip()
                                cliente['telefone'] = limpar_telefone(telefone_texto)
                        except:
                            logger.warning(f"Telefone não disponível para '{nome_cliente}'")
                            cliente['telefone'] = "Telefone não disponível"

                # Extrair endereço (opcional)
                try:
                    endereco_elemento = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, '//button[contains(@data-item-id, "address")]'))
                    )
                    cliente['endereco'] = endereco_elemento.text.strip()
                except:
                    logger.warning(f"Endereço não disponível para '{nome_cliente}'")

                # Extrair site (opcional)
                try:
                    site_elemento = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, '//a[contains(@data-item-id, "authority")]'))
                    )
                    cliente['site'] = site_elemento.get_attribute('href')
                except:
                    logger.warning(f"Site não disponível para '{nome_cliente}'")

                # Adicionar cliente à lista
                clientes_extraidos.append(cliente)
                clientes_unicos.add(nome_cliente)

                logger.info(f"Lead {contador + 1}: {nome_cliente} - {cliente['telefone']}")

                # Atualizar progresso
                progress_callback(contador + 1, quantidade_desejada, cliente)

                contador += 1

            except Exception as e:
                logger.error(f"Erro ao extrair dados do cliente {contador + 1}: {str(e)}")
                contador += 1
                continue

            # Voltar para a lista de resultados
            tentativas_voltar = 0
            max_tentativas_voltar = 3
            sucesso_voltar = False

            while tentativas_voltar < max_tentativas_voltar and not sucesso_voltar:
                try:
                    # Método 1: Botão voltar
                    voltar_button = WebDriverWait(driver, 3).until(
                        EC.element_to_be_clickable((By.XPATH, '//button[@aria-label="Voltar"]'))
                    )
                    voltar_button.click()
                    time.sleep(1.5)
                    sucesso_voltar = True
                    logger.info("Voltou para a lista de resultados usando o botão Voltar")
                except:
                    try:
                        # Método 2: JavaScript history.back()
                        logger.warning("Não foi possível voltar usando o botão, tentando history.back()")
                        driver.execute_script("history.back()")
                        time.sleep(2)

                        # Verificar se voltou para a lista de resultados
                        elementos_apos_voltar = driver.find_elements(By.XPATH, '//a[@class="hfpxzc"]')
                        if len(elementos_apos_voltar) > 0:
                            sucesso_voltar = True
                            logger.info("Voltou para a lista de resultados usando history.back()")
                        else:
                            # Método 3: Tentar clicar em qualquer área fora do card de detalhes
                            try:
                                logger.warning("Não foi possível voltar com history.back(), tentando clicar fora do card")
                                driver.find_element(By.XPATH, '//div[@class="aomaec"]').click()
                                time.sleep(1)
                                sucesso_voltar = True
                            except:
                                # Método 4: Pressionar ESC
                                try:
                                    logger.warning("Tentando pressionar ESC para fechar o card")
                                    webdriver.ActionChains(driver).send_keys(Keys.ESCAPE).perform()
                                    time.sleep(1)
                                    sucesso_voltar = True
                                except:
                                    # Método 5: Recarregar a página e refazer a busca
                                    if tentativas_voltar == max_tentativas_voltar - 1:  # Última tentativa
                                        logger.warning("Tentando recarregar a página e refazer a busca")
                                        driver.refresh()
                                        time.sleep(3)

                                        # Tentar refazer a busca
                                        try:
                                            search_box = WebDriverWait(driver, 10).until(
                                                EC.presence_of_element_located((By.XPATH, '//*[@id="searchboxinput"]'))
                                            )
                                            search_box.clear()
                                            search_box.send_keys(Keys.CONTROL + "a")
                                            search_box.send_keys(Keys.DELETE)
                                            search_box.send_keys(driver.current_url.split('@')[1].split(',')[0])  # Usar coordenadas da URL
                                            search_box.send_keys(Keys.ENTER)
                                            time.sleep(3)
                                            sucesso_voltar = True
                                        except:
                                            logger.error("Falha ao recarregar e refazer a busca")
                    except:
                        logger.error("Falha ao navegar de volta para a lista de resultados")

                tentativas_voltar += 1

            # Se não conseguiu voltar após todas as tentativas, tenta continuar mesmo assim
            if not sucesso_voltar:
                logger.error("Não foi possível voltar para a lista de resultados após várias tentativas")
                # Tentar recarregar a página como último recurso
                try:
                    driver.get('https://www.google.com/maps/')
                    time.sleep(3)
                    search_box = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, '//*[@id="searchboxinput"]'))
                    )
                    search_box.send_keys(palavra_chave_atual)
                    search_box.send_keys(Keys.ENTER)
                    time.sleep(5)
                    logger.info("Página recarregada e busca refeita como último recurso")
                except:
                    logger.error("Falha ao recarregar a página como último recurso")

    except Exception as e:
        logger.error(f'Falha na extração: {str(e)}')

    finally:
        # Informações finais
        total_extraidos = len(clientes_extraidos)
        logger.info(f"Extração concluída. Total de leads extraídos: {total_extraidos}")

        if total_extraidos < quantidade_desejada:
            logger.warning(f"Atenção: foram solicitados {quantidade_desejada} leads, mas apenas {total_extraidos} foram encontrados.")

        return clientes_extraidos

def scroll_down(driver):
    """
    Rola a página para baixo para carregar mais resultados

    Args:
        driver: WebDriver do Selenium
    """
    try:
        # Tenta encontrar o último elemento visível e rolar até ele usando múltiplos seletores
        selectors = [
            '//a[@class="hfpxzc"]',
            '//a[contains(@class, "hfpxzc")]',
            '//div[@role="article"]//a',
            '//div[contains(@class, "Nv2PK")]//a',
            '//div[@data-result-index]//a'
        ]

        elementos = None
        for selector in selectors:
            try:
                elementos = driver.find_elements(By.XPATH, selector)
                if elementos and len(elementos) > 0:
                    break
            except:
                continue

        if elementos:
            driver.execute_script("arguments[0].scrollIntoView({block: 'end'});", elementos[-1])
            # Rola um pouco mais para garantir que novos elementos sejam carregados
            driver.execute_script("window.scrollBy(0, 200);")
        else:
            # Tenta encontrar o painel de resultados e rolar dentro dele
            try:
                painel_selectors = [
                    '//div[@role="feed"]',
                    '//div[contains(@class, "m6QErb")]',
                    '//div[contains(@class, "siAUzd")]'
                ]

                for painel_selector in painel_selectors:
                    try:
                        painel_resultados = driver.find_element(By.XPATH, painel_selector)
                        driver.execute_script("arguments[0].scrollTop = arguments[0].scrollTop + 500", painel_resultados)
                        break
                    except:
                        continue
            except:
                # Se não encontrar o painel, tenta rolar a página principal
                driver.execute_script("window.scrollBy(0, 500);")

            # Tenta clicar no botão "Ver mais resultados" se existir
            try:
                botao_selectors = [
                    '//button[contains(., "Ver mais")]',
                    '//button[contains(., "More results")]',
                    '//button[contains(@class, "VfPpkd-LgbsSe")]'
                ]

                for botao_selector in botao_selectors:
                    try:
                        botao_mais = driver.find_element(By.XPATH, botao_selector)
                        botao_mais.click()
                        logger.info("Clicou no botão 'Ver mais resultados'")
                        time.sleep(1)
                        break
                    except:
                        continue
            except:
                pass
    except Exception as e:
        logger.error(f"Erro ao rolar a página: {str(e)}")
        # Fallback para rolagem simples
        driver.execute_script("window.scrollBy(0, 500);")

    # Aguarda um momento para o carregamento dos elementos
    time.sleep(1)

def df_clientes(clientes_extraidos):
    """
    Converte a lista de clientes para um DataFrame pandas

    Args:
        clientes_extraidos: Lista de dicionários com informações dos clientes

    Returns:
        DataFrame pandas
    """
    df = pd.DataFrame(clientes_extraidos)
    return df

def salvar_clientes(clientes, nome_arquivo):
    """
    Salva os clientes extraídos em um arquivo Excel

    Args:
        clientes: Lista de dicionários com informações dos clientes
        nome_arquivo: Caminho do arquivo onde os dados serão salvos
    """
    try:
        logger.info(f"Salvando {len(clientes)} leads no arquivo: {nome_arquivo}")

        # Criar diretório se não existir
        diretorio = os.path.dirname(nome_arquivo)
        if diretorio and not os.path.exists(diretorio):
            os.makedirs(diretorio)

        # Converter para DataFrame e salvar
        df = df_clientes(clientes)

        # Adicionar timestamp
        hora_extracao = datetime.datetime.now().strftime("%d/%m/%Y %H:%M:%S")
        df['Data de Extração'] = hora_extracao

        # Salvar arquivo
        df.to_excel(nome_arquivo, index=False, engine='openpyxl')
        logger.info(f"Arquivo salvo com sucesso: {nome_arquivo}")
        return True

    except Exception as e:
        logger.error(f"Erro ao salvar arquivo {nome_arquivo}: {str(e)}")
        raise e
